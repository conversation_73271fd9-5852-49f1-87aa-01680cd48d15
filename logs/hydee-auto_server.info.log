|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034679|Thread-434 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/task_info/get_daily_monitoring_detail_list/，请求方式：POST，请求参数为：b'{"date":"2025-03-13","environment":"1","creater":"liuyanxia","is_repair":"","pagenum":1,"pagesize":20}'
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034707|Thread-436 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/task_info/get_daily_monitoring_detail_list/，请求方式：POST，请求参数为：b'{"date":"2025-03-13","environment":"1","creater":"liuyanxia","is_repair":"","pagenum":1,"pagesize":20}'
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034710|Thread-435 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034723|Thread-433 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/menu/，请求方式：GET，请求参数为：<QueryDict: {}>
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034731|Thread-434 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034731|Thread-436 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-435 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-433 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-434 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-436 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-435 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034732|Thread-433 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034791|Thread-437 (recordSystemLog):***********|INFO|commonUtil.py:50|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034795|Thread-438 (recordSystemLog):***********|INFO|commonUtil.py:50|用户 liuyanxia 查询日常监控详细运行情况列表
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034796|Thread-439 (recordSystemLog):***********|INFO|commonUtil.py:50|用户 liuyanxia 查询日常监控详细运行情况列表
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034807|Thread-435 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1754269234806}
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034811|Thread-433 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 1, "authName": "系统配置", "path": "system-configure", "children": [{"id": 7, "authName": "用户管理", "path": "users", "children": []}, {"id": 8, "authName": "角色管理", "path": "roles", "children": []}, {"id": 9, "authName": "权限管理", "path": "rights", "children": []}]}, {"id": 2, "authName": "数据配置", "path": "user-configure", "children": [{"id": 47, "authName": "数据库", "path": "databases", "children": []}, {"id": 48, "authName": "测试参数", "path": "parameters", "children": []}, {"id": 50, "authName": "邮件模板", "path": "emails", "children": []}]}, {"id": 3, "authName": "接口测试", "path": "api-test", "children": [{"id": 16, "authName": "域名管理", "path": "projects", "children": []}, {"id": 17, "authName": "接口管理", "path": "interfaces", "children": []}, {"id": 18, "authName": "案例管理", "path": "api-cases", "children": []}, {"id": 19, "authName": "业务线管理", "path": "businesses", "children": []}, {"id": 54, "authName": "场景管理", "path": "scenes", "children": []}, {"id": 56, "authName": "场景任务", "path": "scene-task", "children": []}, {"id": 57, "authName": "待处理接口管理", "path": "interfaceCompare", "children": []}, {"id": 58, "authName": "接口流量统计", "path": "InterfaceTrafficStatistics", "children": []}]}, {"id": 4, "authName": "自动化监控", "path": "daily-monitoring", "children": [{"id": 66, "authName": "日常监控", "path": "daily-monitoring-list", "children": []}]}, {"id": 6, "authName": "测试助手", "path": "test-helper", "children": [{"id": 43, "authName": "日志列表", "path": "logs", "children": []}, {"id": 44, "authName": "发版列表", "path": "releases", "children": []}, {"id": 51, "authName": "工具列表", "path": "utils", "children": []}, {"id": 52, "authName": "优惠计算器", "path": "money-calculate", "children": []}, {"id": 53, "authName": "请求头转换", "path": "headers", "children": []}]}, {"id": 59, "authName": "鹰眼", "path": "after-sale", "children": [{"id": 60, "authName": "问题列表", "path": "question-list", "children": []}, {"id": 61, "authName": "群聊记录列表", "path": "chatRecord-list", "children": []}, {"id": 62, "authName": "售后问题统计", "path": "question-statistics", "children": []}, {"id": 70, "authName": "数据归档", "path": "data-archiving", "children": []}, {"id": 74, "authName": "群初始化配置", "path": "room-init-config", "children": []}]}, {"id": 71, "authName": "测试用例库", "path": "test-case", "children": [{"id": 63, "authName": "测试用例管理", "path": "testcaseManagement", "children": []}, {"id": 72, "authName": "AI测试用例库", "path": "AI-test-case", "children": []}, {"id": 75, "authName": "AI生成用例", "path": "ai-generate-testcases", "children": []}]}], "meta": {"msg": "获取菜单列表成功", "status": 200}, "timestamp": 1754269234812}
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034821|Thread-435 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.09621191024780273 秒
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034826|Thread-433 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.08835792541503906 秒
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034934|Thread-436 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 20, "total": 1, "data": [{"monitoring_record_id": 28, "scene_id": "1170", "scene_name": "lyx微商城搜索云仓品", "run_result": "fail", "parent_id": "9387", "business_id": 20, "fail_reason": "", "repair_plan": "23423", "tapd_link": null, "is_repair": "0", "environment": "1", "fail_type": "1", "creater": "liuyanxia", "update_time": "2025-04-11T14:11:53", "businessName": "微商城9", "task_name": "企微推送26验证", "scene_error_count": 14}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1754269234934}
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034941|Thread-436 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.22685480117797852 秒
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034946|Thread-434 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 20, "total": 1, "data": [{"monitoring_record_id": 28, "scene_id": "1170", "scene_name": "lyx微商城搜索云仓品", "run_result": "fail", "parent_id": "9387", "business_id": 20, "fail_reason": "", "repair_plan": "23423", "tapd_link": null, "is_repair": "0", "environment": "1", "fail_type": "1", "creater": "liuyanxia", "update_time": "2025-04-11T14:11:53", "businessName": "微商城9", "task_name": "企微推送26验证", "scene_error_count": 14}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1754269234946}
|hydee-auto_server|************|a18e980a333e48a0b0cb1f9ffe012b48|||20250804090034950|Thread-434 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.2665688991546631 秒
