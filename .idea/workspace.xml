<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2fe8f7cb-7ff0-42c5-897c-0d6fb0517715" name="Changes" comment="自动化流程整合改造开发">
      <change beforePath="$PROJECT_DIR$/.idea/hydee_auto_server.iml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/hydee_auto_server.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hydee_auto_server/settings.py" beforeDir="false" afterPath="$PROJECT_DIR$/hydee_auto_server/settings.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test_case/views.py" beforeDir="false" afterPath="$PROJECT_DIR$/test_case/views.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2y271zbdGUMyNfIqnLxGHdlMg5w" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python tests.debug.executor&quot;: &quot;Debug&quot;,
    &quot;Python.debug.executor&quot;: &quot;Debug&quot;,
    &quot;Python.get_tapd_info.executor&quot;: &quot;Debug&quot;,
    &quot;Python.tasks (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.tasks.executor&quot;: &quot;Run&quot;,
    &quot;Python.testcase_tools.executor&quot;: &quot;Run&quot;,
    &quot;Python.views.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Documents/autotest/hydee_auto_server/ai_agent&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/ai_agent" />
      <recent name="$PROJECT_DIR$/ai_agent/services" />
      <recent name="$PROJECT_DIR$/ai_agent/agents" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/ai_agent/agents" />
      <recent name="$PROJECT_DIR$/ai_agent" />
    </key>
  </component>
  <component name="RunManager" selected="Python.debug">
    <configuration name="课3" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/课3.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="debug" type="PythonConfigurationType" factoryName="Python">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.10 (hydee_auto_server)" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/manage.py" />
      <option name="PARAMETERS" value="runserver" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="get_tapd_info" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test_case/servers" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_case/servers/get_tapd_info.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="tasks" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test_case" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_case/tasks.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/langchain" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="testcase_tools" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="hydee_auto_server" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ai_agent/tools" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ai_agent/tools/testcase_tools.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.debug" />
      <item itemvalue="Python.课3" />
      <item itemvalue="Python.get_tapd_info" />
      <item itemvalue="Python.tasks" />
      <item itemvalue="Python.test" />
      <item itemvalue="Python.testcase_tools" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.课3" />
        <item itemvalue="Python.testcase_tools" />
        <item itemvalue="Python.test" />
        <item itemvalue="Python.tasks" />
        <item itemvalue="Python.get_tapd_info" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-50da183f06c8-2887949eec09-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-233.13135.95" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2fe8f7cb-7ff0-42c5-897c-0d6fb0517715" name="Changes" comment="" />
      <created>1749020603512</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749020603512</updated>
      <workItem from="1749020604866" duration="340000" />
      <workItem from="1749024286280" duration="52241000" />
      <workItem from="1749518587873" duration="46339000" />
      <workItem from="1750061799892" duration="61212000" />
      <workItem from="1753412868953" duration="113000" />
      <workItem from="1753845239339" duration="831000" />
      <workItem from="1753857880583" duration="166000" />
      <workItem from="1753858057507" duration="17487000" />
      <workItem from="1753947568751" duration="5054000" />
      <workItem from="1754014314218" duration="5868000" />
    </task>
    <task id="LOCAL-00001" summary="智能体优化">
      <option name="closed" value="true" />
      <created>1749028258544</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749028258544</updated>
    </task>
    <task id="LOCAL-00002" summary="原大模型账号欠费,更新大模型账号">
      <option name="closed" value="true" />
      <created>1749110878365</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749110878365</updated>
    </task>
    <task id="LOCAL-00003" summary="智能体工具修饰符增加参数need_llm_process参数;流式输出接口返回的内容编码转义成中文">
      <option name="closed" value="true" />
      <created>1749115057211</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749115057211</updated>
    </task>
    <task id="LOCAL-00004" summary="工具返回消息调整">
      <option name="closed" value="true" />
      <created>1749177479473</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749177479473</updated>
    </task>
    <task id="LOCAL-00005" summary="智能体优化;用例池AI合并用例优化">
      <option name="closed" value="true" />
      <created>1749202141003</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1749202141003</updated>
    </task>
    <task id="LOCAL-00006" summary="skywalking获取sql提示词优化">
      <option name="closed" value="true" />
      <created>1749202225915</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1749202225915</updated>
    </task>
    <task id="LOCAL-00007" summary="AI生成用例增加字段:需求分析结果">
      <option name="closed" value="true" />
      <created>1749204137192</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1749204137192</updated>
    </task>
    <task id="LOCAL-00008" summary="修改生成合并用例的提示词">
      <option name="closed" value="true" />
      <created>1749449516767</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1749449516767</updated>
    </task>
    <task id="LOCAL-00009" summary="工具从skywalking获取sql-增加日志记录">
      <option name="closed" value="true" />
      <created>1749449558357</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1749449558357</updated>
    </task>
    <task id="LOCAL-00010" summary="从tapd获取bug数据">
      <option name="closed" value="true" />
      <created>1749465728201</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1749465728201</updated>
    </task>
    <task id="LOCAL-00011" summary="修改model中定义的索引">
      <option name="closed" value="true" />
      <created>1749469659983</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1749469659983</updated>
    </task>
    <task id="LOCAL-00012" summary="修改model中定义的索引">
      <option name="closed" value="true" />
      <created>1749469764048</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1749469764048</updated>
    </task>
    <task id="LOCAL-00013" summary="完善TapdBugInfo模型信息">
      <option name="closed" value="true" />
      <created>1749470748919</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1749470748919</updated>
    </task>
    <task id="LOCAL-00014" summary="修改TapdBugInfo模型部分索引name">
      <option name="closed" value="true" />
      <created>1749521145036</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1749521145036</updated>
    </task>
    <task id="LOCAL-00015" summary="tapd获取bug数据,增加bug关联的迭代字段获取">
      <option name="closed" value="true" />
      <created>1749535568391</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1749535568391</updated>
    </task>
    <task id="LOCAL-00016" summary="工具入参支持传入参数用户信息">
      <option name="closed" value="true" />
      <created>1749538962844</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1749538962844</updated>
    </task>
    <task id="LOCAL-00017" summary="修复bug日常监控列表页码显示异常bug">
      <option name="closed" value="true" />
      <created>1749547543558</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1749547543558</updated>
    </task>
    <task id="LOCAL-00018" summary="agent分支遗漏代码找回">
      <option name="closed" value="true" />
      <created>1750045791828</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750045791828</updated>
    </task>
    <task id="LOCAL-00019" summary="skywalking获取SQL增加日志记录">
      <option name="closed" value="true" />
      <created>1750298119392</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1750298119392</updated>
    </task>
    <task id="LOCAL-00020" summary="新增初始向量化接口">
      <option name="closed" value="true" />
      <created>1750303230601</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1750303230601</updated>
    </task>
    <task id="LOCAL-00021" summary="自动化数据统计周报增加统计任务">
      <option name="closed" value="true" />
      <created>1750387109504</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750387109504</updated>
    </task>
    <task id="LOCAL-00022" summary="自动化数据统计周报增加统计任务">
      <option name="closed" value="true" />
      <created>1751014678460</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1751014678460</updated>
    </task>
    <task id="LOCAL-00023" summary="自动化数据统计周报增加统计任务">
      <option name="closed" value="true" />
      <created>1751014807129</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1751014807129</updated>
    </task>
    <task id="LOCAL-00024" summary="还原weekly.py文件日志记录代码">
      <option name="closed" value="true" />
      <created>1751017183115</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1751017183115</updated>
    </task>
    <task id="LOCAL-00025" summary="解决Matplotlib 3.4+ 版本中，savefig() 不再支持 encoding 参数的问题">
      <option name="closed" value="true" />
      <created>1751611633832</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1751611633832</updated>
    </task>
    <task id="LOCAL-00026" summary="AI生成用例加入日志记录">
      <option name="closed" value="true" />
      <created>1751942554894</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1751942554894</updated>
    </task>
    <task id="LOCAL-00027" summary="AI生成用例加入日志记录">
      <option name="closed" value="true" />
      <created>1751943313997</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1751943313997</updated>
    </task>
    <task id="LOCAL-00028" summary="AI生成用例加入日志记录">
      <option name="closed" value="true" />
      <created>1751947769738</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1751947769738</updated>
    </task>
    <task id="LOCAL-00029" summary="AI生成用例加入日志记录">
      <option name="closed" value="true" />
      <created>1751957331181</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1751957331181</updated>
    </task>
    <task id="LOCAL-00030" summary="AI生成测试用例增加错误处理">
      <option name="closed" value="true" />
      <created>1751958317062</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1751958317062</updated>
    </task>
    <task id="LOCAL-00031" summary="AI生成测试用例增加错误处理">
      <option name="closed" value="true" />
      <created>1751959848352</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1751959848352</updated>
    </task>
    <task id="LOCAL-00032" summary="AI生成测试用例优化;更换LLM模型">
      <option name="closed" value="true" />
      <created>1751962842590</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1751962842590</updated>
    </task>
    <task id="LOCAL-00033" summary="更换向量化模型;优化用例合并的提示词">
      <option name="closed" value="true" />
      <created>1752028840717</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1752028840717</updated>
    </task>
    <task id="LOCAL-00034" summary="获取tapd需求时,增加入库字段:业务线">
      <option name="closed" value="true" />
      <created>1752135586189</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1752135586189</updated>
    </task>
    <task id="LOCAL-00035" summary="优化召回用例导出">
      <option name="closed" value="true" />
      <created>1752215160355</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1752215160355</updated>
    </task>
    <task id="LOCAL-00036" summary="优化合并用例提示词">
      <option name="closed" value="true" />
      <created>1752637123930</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1752637123930</updated>
    </task>
    <task id="LOCAL-00037" summary="新增测试用例列表接口">
      <option name="closed" value="true" />
      <created>1753956383712</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753956383712</updated>
    </task>
    <task id="LOCAL-00038" summary="自动化流程整合改造开发">
      <option name="closed" value="true" />
      <created>1754013652000</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1754013652000</updated>
    </task>
    <option name="localTasksCounter" value="39" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="AI生成用例增加字段:需求分析结果" />
    <MESSAGE value="修改生成合并用例的提示词" />
    <MESSAGE value="工具从skywalking获取sql-增加日志记录" />
    <MESSAGE value="从tapd获取bug数据" />
    <MESSAGE value="修改model中定义的索引" />
    <MESSAGE value="完善TapdBugInfo模型信息" />
    <MESSAGE value="修改TapdBugInfo模型部分索引name" />
    <MESSAGE value="tapd获取bug数据,增加bug关联的迭代字段获取" />
    <MESSAGE value="工具入参支持传入参数用户信息" />
    <MESSAGE value="修复bug日常监控列表页码显示异常bug" />
    <MESSAGE value="agent分支遗漏代码找回" />
    <MESSAGE value="skywalking获取SQL增加日志记录" />
    <MESSAGE value="新增初始向量化接口" />
    <MESSAGE value="自动化数据统计周报增加统计任务" />
    <MESSAGE value="还原weekly.py文件日志记录代码" />
    <MESSAGE value="解决Matplotlib 3.4+ 版本中，savefig() 不再支持 encoding 参数的问题" />
    <MESSAGE value="AI生成用例加入日志记录" />
    <MESSAGE value="AI生成测试用例增加错误处理" />
    <MESSAGE value="AI生成测试用例优化;更换LLM模型" />
    <MESSAGE value="更换向量化模型;优化用例合并的提示词" />
    <MESSAGE value="获取tapd需求时,增加入库字段:业务线" />
    <MESSAGE value="优化召回用例导出" />
    <MESSAGE value="优化合并用例提示词" />
    <MESSAGE value="新增测试用例列表接口" />
    <MESSAGE value="自动化流程整合改造开发" />
    <option name="LAST_COMMIT_MESSAGE" value="自动化流程整合改造开发" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/hydee_auto_server$testcase_tools.coverage" NAME="testcase_tools Coverage Results" MODIFIED="1749089746719" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ai_agent/tools" />
    <SUITE FILE_PATH="coverage/hydee_auto_server$tasks__1_.coverage" NAME="tasks (1) Coverage Results" MODIFIED="1751598286236" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/interface_test" />
    <SUITE FILE_PATH="coverage/hydee_auto_server$debug.coverage" NAME="debug Coverage Results" MODIFIED="1754034107597" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/hydee_auto_server$views.coverage" NAME="views Coverage Results" MODIFIED="1749024560321" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ai_agent" />
    <SUITE FILE_PATH="coverage/hydee_auto_server$tasks.coverage" NAME="tasks Coverage Results" MODIFIED="1749524616691" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test_case" />
    <SUITE FILE_PATH="coverage/hydee_auto_server$get_tapd_info.coverage" NAME="get_tapd_info Coverage Results" MODIFIED="1752135261421" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test_case/servers" />
  </component>
</project>