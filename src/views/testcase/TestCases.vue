<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试用例管理</el-breadcrumb-item>
      <el-breadcrumb-item>用例列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row>
        <el-tabs v-model="testCaseListQuery.testcase_type"
                 @tab-click="getCaseList">
          <el-tab-pane label="全部用例" name="all_testcase">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-input placeholder="用例场景"
                          v-model="testCaseListQuery.case_scene"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="迭代名称"
                          v-model="testCaseListQuery.iteration_name"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="需求ID"
                          v-model="testCaseListQuery.requirement_id"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-select placeholder="业务线"
                           v-model="testCaseListQuery.business_id"
                           clearable
                           multiple
                           filterable>
                  <el-option v-for="item in businessList"
                             :key="item.id"
                             :label="item.label"
                             :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="更新人员"
                          v-model="testCaseListQuery.update_person"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="queryList">查 询</el-button>
                <el-button type="primary" @click="queryReset('all_testcase')">重 置</el-button>
                <el-button type="warning" @click="queryReset('core_testcase')">批量删除</el-button>
                <el-button type="warning" @click="queryReset('core_testcase')">新增用例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="冒烟用例" name="smoke_testcase">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-input placeholder="迭代名称"
                          v-model="testCaseListQuery.iteration_name"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="需求ID"
                          v-model="testCaseListQuery.requirement_id"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="开发人员"
                          v-model="testCaseListQuery.developer"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="执行人员"
                          v-model="testCaseListQuery.operator"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-select placeholder="执行结果"
                           v-model="testCaseListQuery.operator_result"
                           multiple
                           clearable>
                  <el-option value="0" label="未执行"></el-option>
                  <el-option value="1" label="通过"></el-option>
                  <el-option value="2" label="未通过"></el-option>
                </el-select>
              </el-col>
              <el-col :span="7">
                <el-button type="primary" @click="queryList">查 询</el-button>
                <el-button type="primary" @click="queryReset('smoke_testcase')">重 置</el-button>
                <el-button type="warning" @click="batchExcuteSmokeCase()">批量维护冒烟结果</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="核心用例" name="core_testcase">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-input placeholder="迭代名称"
                          v-model="testCaseListQuery.iteration_name"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="需求ID"
                          v-model="testCaseListQuery.requirement_id"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="用例场景"
                          v-model="testCaseListQuery.case_scene"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="场景ID"
                          v-model="testCaseListQuery.scene_id"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
              <el-select placeholder="推送状态"
                         v-model="testCaseListQuery.push_statues"
                         clearable>
                <el-option value="0" label="未推送"></el-option>
                <el-option value="1" label="已推送"></el-option>
              </el-select>
            </el-col>
              <el-col :span="3">
                <el-input placeholder="更新人员"
                          v-model="testCaseListQuery.update_person"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="queryList">查 询</el-button>
                <el-button type="primary" @click="queryReset('core_testcase')">重 置</el-button>
                <el-button type="warning" @click="queryReset('core_testcase')">推送用例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="AI生成用例" name="ai_testcase">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-input placeholder="迭代名称"
                          v-model="testCaseListQuery.iteration_name"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="需求ID"
                          v-model="testCaseListQuery.requirement_id"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-select placeholder="业务线"
                           v-model="testCaseListQuery.business_id"
                           clearable
                           multiple
                           filterable>
                  <el-option v-for="item in businessList"
                             :key="item.id"
                             :label="item.label"
                             :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-input placeholder="更新人员"
                          v-model="testCaseListQuery.update_person"
                          clearable
                          @keyup.enter.native="queryList"
                >
                </el-input>
              </el-col>
              <el-col :span="3">
              <el-select placeholder="召回状态"
                         v-model="testCaseListQuery.callback_status"
                         clearable>
                <el-option value="0" label="未召回"></el-option>
                <el-option value="1" label="已召回"></el-option>
              </el-select>
            </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="queryList">查 询</el-button>
                <el-button type="primary" style="margin-left: 10px;" @click="queryReset('ai_testcase')">重 置</el-button>
                <el-dropdown trigger="click">
                <el-button type="warning" style="margin-left: 10px;">
                  批量修改召回状态 <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" trigger="click">
                  <el-dropdown-item>
                    <el-button size="mini" type="danger" @click="queryReset('ai_testcase')">
                      批量召回
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button size="mini" type="primary" @click="queryReset('ai_testcase')">
                      批量取消召回
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              </el-col>
<!--              <el-col :span="4">-->
<!--            </el-col>-->
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-row>
      <el-row>
        <div class="table-container">
          <el-table :data="testCaseList" border v-loading="getCaseListLoading" @selection-change="handleSelectionCase">
            <el-table-column type="selection" width="40" fixed="left"></el-table-column>
            <el-table-column label="迭代名称" prop="iteration_name" width="180" show-overflow-tooltip
                             align="center"></el-table-column>
            <el-table-column label="业务线" prop="businessName" width="100" show-overflow-tooltip
                             align="center"></el-table-column>
            <el-table-column label="需求ID" prop="requirement_id" width="100" show-overflow-tooltip
                             align="center"></el-table-column>
            <el-table-column label="用例场景" prop="case_scene" width="180" show-overflow-tooltip
                             align="center"></el-table-column>
            <el-table-column label="用例详情" width="100" align="center">
              <template #default="scope">
                <el-popover
                  placement="top"
                  width="800"
                  trigger="click"
                  popper-class="dark-popover"
                >
                  <div v-html="getCaseDetailContent(scope.row)"
                       style="white-space: pre-line; font-size: 12px"></div>
                  <div slot="reference"
                       class="cell-content"
                       style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                    点击查看
                  </div>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="开发人员" prop="developer" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'smoke_testcase'"></el-table-column>
            <el-table-column label="执行结果" prop="operator_result" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'smoke_testcase'">
              <template v-slot="slotProps">
                <span v-if="slotProps.row.operator_result==='1'"
                      style="color: #98D7B6; font-weight: bold; font-size: 13px">通过</span>
                <span v-if="slotProps.row.operator_result==='0'"
                      style="color: #969b96; font-weight: bold; font-size: 13px">未执行</span>
                <span v-if="slotProps.row.operator_result==='2'"
                      style="color: #FF9C99; font-weight: bold; font-size: 13px">未通过</span>
              </template>
            </el-table-column>
            <el-table-column label="执行结果说明" prop="operator_result_remark" width="120" show-overflow-tooltip
                             align="center" v-if="testCaseListQuery.testcase_type === 'smoke_testcase'"></el-table-column>
            <el-table-column label="图片" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'smoke_testcase'">
              <template slot-scope="scope">
                <template v-if="hasImage(scope.row.file_url)">
                  <el-link @click="previewImages(scope.row.file_url)" style="font-size: 12px;">查看图片<i
                    class="el-icon-view el-icon--right"></i></el-link>
                </template>
                <template v-else>
                  <span>无</span>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="场景ID" prop="case_scene_id" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'core_testcase'"></el-table-column>
            <el-table-column label="未合并用例" prop="case_scene_id" width="120" align="center"
                             v-if="testCaseListQuery.testcase_type === 'core_testcase'"></el-table-column>
            <el-table-column label="推送状态" prop="case_scene_id" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'core_testcase'"></el-table-column>
            <el-table-column label="执行人" prop="operator" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type === 'smoke_testcase'"></el-table-column>
            <el-table-column label="召回状态" prop="case_scene_id" width="90" align="center"
                             v-if="testCaseListQuery.testcase_type === 'ai_testcase'">
              <template slot-scope="scope">
              <span v-if="scope.row.status==='1'" style="color: #67C23A;">已召回</span>
              <span v-else style="color: #f35d58;">未召回</span>
            </template>
            </el-table-column>
            <el-table-column label="更新人" prop="update_person" width="100" align="center"
                             v-if="testCaseListQuery.testcase_type !== 'smoke_testcase'"></el-table-column>
            <el-table-column label="更新时间" prop="update_time" width="150" align="center"
                             v-if="testCaseListQuery.testcase_type !== 'smoke_testcase'"></el-table-column>
            <!--全部用例操作栏-->
            <el-table-column label="操作" fixed="right" width="250" align="center"
                             v-if="testCaseListQuery.testcase_type === 'all_testcase'">
              <template v-slot="slotProps">
                <el-button type="primary" size="mini" class="compact-button"
                           @click="handleUpdateCase(slotProps.row)">编辑用例</el-button>
                <el-button type="warning" size="mini" class="compact-button"
                           @click="copyCase(slotProps.row)">复制用例</el-button>
                <el-button type="danger" size="mini" class="compact-button"
                           @click="deleteCase(slotProps.row)">删除用例</el-button>
              </template>
            </el-table-column>
            <!--冒烟用例操作栏-->
            <el-table-column label="操作" fixed="right" width="210" align="center"
                             v-if="testCaseListQuery.testcase_type === 'smoke_testcase'">
              <template v-slot="slotProps">
                <el-button type="success" size="mini" class="compact-button"
                           @click="executeSmokeCase(slotProps.row)">维护执行结果</el-button>
                <el-button type="primary" size="mini" class="compact-button"
                           @click="handleUpdateCase(slotProps.row)">编辑用例</el-button>
              </template>
            </el-table-column>
            <!--核心用例操作栏-->
            <el-table-column label="操作" fixed="right" width="320" align="center"
                             v-if="testCaseListQuery.testcase_type === 'core_testcase'">
              <template v-slot="slotProps">
                <el-button type="primary" size="mini" class="compact-button"
                           @click="handleUpdateCase(slotProps.row)">编辑用例</el-button>
                <el-button type="warning" size="mini" class="compact-button"
                           @click="addAutoCase(slotProps.row)">实现自动化</el-button>
                <el-button type="success" size="mini" class="compact-button"
                           @click="relatedCase(slotProps.row)">关联任务</el-button>
                <el-button type="danger" size="mini" class="compact-button"
                           @click="matchCase(slotProps.row)">匹配用例</el-button>
              </template>
            </el-table-column>
            <!--AI生成用例操作栏-->
            <el-table-column label="操作" fixed="right" width="140" align="center"
                             v-if="testCaseListQuery.testcase_type === 'ai_testcase'">
              <template v-slot="slotProps">
                <div class="action-buttons">
                  <el-button type="primary" size="mini" class="compact-button"
                             @click="handleUpdateAICase(slotProps.row)">编辑用例</el-button>
                  <el-button type="success" size="mini" class="compact-button"
                             v-if="slotProps.row.status === '0'"
                             @click="submitUpdateAICaseStatus(slotProps.row, '1')">召回用例</el-button>
                  <el-button type="danger" size="mini" class="compact-button"
                             v-if="slotProps.row.status === '1'"
                             @click="submitUpdateAICaseStatus(slotProps.row, '0')">取消召回用例</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="testCaseListQuery.pagenum"
          :page-size="testCaseListQuery.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>

    <!--编辑用例弹框-->
    <el-dialog
        :visible.sync="handleUpdateCaseDialogVisible"
        title = "编辑用例"
        width="80%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="testCaseDetailForm" :rules="testCaseDetailFormRules" ref="testCaseDetailFormRef" key="testCaseDetailFormRef">
          <el-row>
            <el-col :span="9">
              <el-form-item label="业务线:" class="bold-label">
                <span>{{ testCaseDetailForm.businessName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属系统:" class="bold-label">
                <span>{{ testCaseDetailForm.system }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="迭代名称:" class="bold-label">
                <span>{{ testCaseDetailForm.iteration_name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="9">
              <el-form-item label="用例场景:" class="bold-label">
                <span>{{ testCaseDetailForm.case_scene }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="模块:" class="bold-label">
                <span>{{ testCaseDetailForm.module }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="前置条件:" class="bold-label" prop="premise">
              <el-input
                type="textarea"
                :rows="3"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.premise"
                placeholder="请输入前置条件"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="测试步骤:" class="bold-label" prop="test_steps">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.test_steps"
                placeholder="请输入测试步骤"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="预期结果:" class="bold-label" prop="expected_result">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.expected_result"
                placeholder="请输入预期结果"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelEditCase">取 消</el-button>
              <el-button type="primary" @click="submitUpdateCase()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>
    <!--编辑AI用例弹框-->
    <el-dialog
        :visible.sync="handleUpdateAICaseDialogVisible"
        title = "编辑用例"
        width="80%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="aiCaseDetailForm" :rules="aiCaseDetailFormRules" ref="testCaseDetailFormRef" key="testCaseDetailFormRef">
          <el-row>
            <el-col :span="8">
              <el-form-item label="用例场景" prop="case_scene">
                <el-input v-model="aiCaseDetailForm.case_scene" placeholder="请输入用例场景"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属系统" prop="system">
                <el-input v-model="aiCaseDetailForm.system" placeholder="请输入所属系统"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模块" prop="module">
                <el-input v-model="aiCaseDetailForm.module" placeholder="请输入模块"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="前置条件:" class="bold-label" prop="premise">
              <el-input
                type="textarea"
                :rows="3"
                maxlength="1000"
                show-word-limit
                v-model="aiCaseDetailForm.premise"
                placeholder="请输入前置条件"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="测试步骤:" class="bold-label" prop="test_steps">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="aiCaseDetailForm.test_steps"
                placeholder="请输入测试步骤"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="预期结果:" class="bold-label" prop="expected_result">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="aiCaseDetailForm.expected_result"
                placeholder="请输入预期结果"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelAICaseEdit">取 消</el-button>
              <el-button type="primary" @click="submitUpdateAICase()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>
    <!--用例执行弹窗-->
    <el-dialog
        :visible.sync="handleExecuteSmokeCaseDialogVisible"
        title = "执行冒烟用例"
        width="80%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="smokeCaseDetailForm">
          <el-row>
            <el-col :span="9">
              <el-form-item label="业务线:" class="bold-label">
                <span>{{ smokeCaseDetailForm.businessName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属系统:" class="bold-label">
                <span>{{ smokeCaseDetailForm.system }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="迭代名称:" class="bold-label">
                <span>{{ smokeCaseDetailForm.iteration_name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="9">
              <el-form-item label="用例场景:" class="bold-label">
                <span>{{ smokeCaseDetailForm.case_scene }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="模块:" class="bold-label">
                <span>{{ smokeCaseDetailForm.module }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="执行结果:" class="bold-label" prop="is_repair" style="color: #df5000">
                <div slot="label" style="color: #df5000">执行结果:</div>
                <el-select
                  v-model="smokeCaseDetailForm.operator_result"
                  size="mini"
                  style="width: 120px;"
                >
                  <el-option value="0" label="未执行"></el-option>
                  <el-option value="1" label="执行通过"></el-option>
                  <el-option value="2" label="执行不通过"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
              <el-form-item label="前置条件:" class="bold-label">
                <span>{{ smokeCaseDetailForm.premise }}</span>
              </el-form-item>
          </el-row>
          <el-row>
              <el-form-item label="测试步骤:" class="bold-label">
                <!-- 调用 formatSteps 方法并传入 test_steps -->
                <div v-for="(step, index) in formatSteps(smokeCaseDetailForm.test_steps)" :key="index">
                  {{ step }}
                </div>
              </el-form-item>
          </el-row>
          <el-row>
              <el-form-item label="预期结果:" class="bold-label">
                <div v-for="(step, index) in formatSteps(smokeCaseDetailForm.expected_result)" :key="index">
                  {{ step }}
                </div>
              </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="执行结果说明:" class="bold-label" prop="operator_result_remark">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="smokeCaseDetailForm.operator_result_remark"
                placeholder="选填,请输入内容或粘贴图片(支持jpg/png格式，单张图片不超过5MB,限制3张图片)"
                @paste.native="handlePastePic"
                ref="replyInput"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <!-- 图片上传部分 -->
            <el-form-item label="上传图片:">
              <el-upload
                :action="uploadPath"
                list-type="picture-card"
                :multiple="true"
                :limit="3"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handFileList"
                :auto-upload="true">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  支持jpg/png格式，单张图片不超过5MB
                </div>
              </el-upload>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelExecuteEdit">取 消</el-button>
              <el-button type="primary" @click="submitExecuteCase()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>
    <!--批量执行冒烟-->
    <el-dialog
        :visible.sync="batchEditSmokeCaseDialogVisible"
        title = "批量执行冒烟用例"
        width="65%">
      <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
      <el-form label-width="140px" :model="smokeCaseDetailForm">
        <el-row>
          <el-col :span="15">
              <el-form-item label="迭代名称:" class="bold-label">
                <span>{{ smokeCaseDetailForm.iteration_name }}</span>
              </el-form-item>
          </el-col>
          <el-col :span="7">
              <el-form-item label="执行结果:" class="bold-label" prop="is_repair" style="color: #df5000">
                <div slot="label" style="color: #df5000">执行结果:</div>
                <el-select
                  v-model="smokeCaseDetailForm.operator_result"
                  size="mini"
                  style="width: 120px;"
                >
                  <el-option value="0" label="未执行"></el-option>
                  <el-option value="1" label="执行通过"></el-option>
                  <el-option value="2" label="执行不通过"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        <el-row>
            <el-form-item label="执行结果说明:" class="bold-label" prop="operator_result_remark">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="smokeCaseDetailForm.operator_result_remark"
                placeholder="选填,请输入内容或粘贴图片(支持jpg/png格式，单张图片不超过5MB,限制3张图片)"
                @paste.native="handlePastePic"
                ref="replyInput"></el-input>
            </el-form-item>
          </el-row>
        <el-row>
            <!-- 图片上传部分 -->
            <el-form-item label="上传图片:">
              <el-upload
                :action="uploadPath"
                list-type="picture-card"
                :multiple="true"
                :limit="3"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handFileList"
                :auto-upload="true">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  支持jpg/png格式，单张图片不超过5MB
                </div>
              </el-upload>
            </el-form-item>
          </el-row>
        <el-row style="text-align: right">
            <span>
              <el-button @click="cancelBatchEdit">取 消</el-button>
              <el-button type="primary" @click="submitExecuteCase()">保 存</el-button>
            </span>
        </el-row>
      </el-form>
    </el-dialog>
    <!--图片预览弹框-->
    <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;"
            >
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
          <div v-else style="text-align: center; padding: 50px;">
            暂无可用图片
          </div>
        </div>
      </el-dialog>
  </div>
</template>

<script>
import { baseUrl } from '../../main'
import EditSceneDrawer from '../../components/EditSceneDrawer.vue'
export default {
  components: {
    // EditSceneDrawer
  },
  data() {
    return {
      model: 'test_case_info',
      queryDate: [],
      testCaseList: [], // 日常监控列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      report_id: this.$route.query.report_id,
      testCaseListQuery: { // 查询栏
        testcase_type: 'all_testcase',
        case_scene: '',
        scene_id: '',
        iteration_name: '',
        requirement_id: '',
        business_id: '',
        developer: '',
        operator: '',
        operator_result: '',
        update_person: '',
        push_statues: '',
        callback_status: '',
        pagenum: 1,
        pagesize: 5
      },
      total: 0,
      businessList: [],
      getCaseListLoading: false,
      handleUpdateCaseDialogVisible: false,
      handleExecuteSmokeCaseDialogVisible: false,
      batchEditSmokeCaseDialogVisible: false,
      handleUpdateAICaseDialogVisible: false, // 控制编辑AI用例弹框的显示
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      fileList: [],
      smokeCaseDetailForm: {},
      aiCaseDetailForm: {},
      testCaseDetailForm: {
        businessName: '',
        system: '',
        iteration_name: '',
        case_scene: '',
        module: '',
        premise: '',
        test_steps: '',
        expected_result: ''
      },
      selectedRows: [],
      testCaseDetailFormRules: {
        test_steps: [
          { required: true, message: '请输入测试步骤', trigger: 'change' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'change' }
        ]
      },
      aiCaseDetailFormRules: {
        case_scene: [
          { required: true, message: '请输入用例场景', trigger: 'change' }
        ],
        test_steps: [
          { required: true, message: '请输入测试步骤', trigger: 'change' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.testCaseListQuery.testcase_type = 'all_testcase' // 确保 DOM 更新后生效
    })
  },
  created() {
    this.getBusinessList()
  },
  activated() {
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)
    this.queryReset()
  },
  deactivated() {
    // 清理事件监听器
    window.removeEventListener('keydown', this.handleKeydown)
  },
  computed: {
    // 当前图片的 src
    currentImageSrc() {
      const currentImage = this.previewImagesList[this.currentImageIndex]
      return currentImage
    },
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}question/question_attachment_upload/`
    },
    // 将 content 按编号分割并格式化
    formattedSteps() {
      // 使用正则表达式按编号分割
      const steps = this.content.split(/(?=\d+、)/).filter(step => step.trim())
      return steps
    }
  },
  methods: {
    // ==================== 获取列表基础数据相关方法 ====================
    // 查询按钮方法
    queryList() {
      this.testCaseListQuery.pagenum = 1
      this.getCaseList()
    },
    // 重置按钮方法
    queryReset(tsetCaseType) {
      this.testCaseListQuery = { // 查询栏
        testcase_type: tsetCaseType,
        case_scene: '',
        iteration_name: '',
        requirement_id: '',
        scene_id: '',
        business_id: '',
        developer: '',
        operator: '',
        operator_result: '',
        update_person: '',
        push_statues: '',
        callback_status: '',
        pagenum: 1,
        pagesize: 5
      }
      this.getCaseList()
    },
    // 调整每页条数
    handleSizeChange(newSize) {
      this.testCaseListQuery.pagesize = newSize
      this.getCaseList()
    },
    // 翻页
    handleCurrentChange(newPage) {
      this.testCaseListQuery.pagenum = newPage
      this.getCaseList()
    },
    // 获取用例列表
    async getCaseList() {
      this.getCaseListLoading = true
      const { data: res } = await this.$http.post(this.model + '/get_testcases_list/', this.testCaseListQuery)
      if (res.meta.status !== 200) return this.$message.error('获取用例列表失败')
      this.total = res.data.total
      this.testCaseList = res.data.data.map(item => ({
        ...item
      }))
      this.getCaseListLoading = false
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    // ==================== 编辑用例相关方法 ====================
    // 编辑用例
    async handleUpdateCase(row, type = null) {
      const objString = JSON.stringify(row)
      this.testCaseDetailForm = JSON.parse(objString)
      this.handleUpdateCaseDialogVisible = true
    },
    // 取消编辑用例
    cancelEditCase() {
      this.testCaseDetailForm = {
        businessName: '',
        system: '',
        iteration_name: '',
        case_scene: '',
        module: '',
        premise: '',
        test_steps: '',
        expected_result: ''
      }
      this.handleUpdateCaseDialogVisible = false
    },
    // 保存编辑用例
    async submitUpdateCase() {
      this.$refs.testCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        let selectedIds = []
        if (this.selectedRows.length === 0 && this.testCaseDetailForm) {
          selectedIds = [this.testCaseDetailForm.id]
        } else {
          selectedIds = this.selectedRows.map(row => row.id)
        }
        const updateData = {
          id: selectedIds,
          expected_result: this.testCaseDetailForm.expected_result,
          test_steps: this.testCaseDetailForm.test_steps,
          premise: this.testCaseDetailForm.premise
        }
        const { data: res } = await this.$http.post(this.model + '/update_smoke_case/', updateData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.testCaseDetailForm = {
          businessName: '',
          system: '',
          iteration_name: '',
          case_scene: '',
          module: '',
          premise: '',
          test_steps: '',
          expected_result: ''
        }
        this.handleUpdateCaseDialogVisible = false
        await this.queryList()
      })
    },
    // ==================== 全部用例tab方法 ====================
    copyCase(row) {
      const id = row.id
      // todo:调用复制用例接口
    },
    // ==================== 冒烟用例tab方法 ====================
    // 执行冒烟用例
    async executeSmokeCase(row) {
      const objString = JSON.stringify(row)
      this.smokeCaseDetailForm = JSON.parse(objString)

      // 确保 file_url 是数组格式
      if (this.smokeCaseDetailForm.file_url) {
        let fileUrls = this.smokeCaseDetailForm.file_url

        // 如果 file_url 是字符串，尝试解析为数组
        if (typeof fileUrls === 'string') {
          try {
            fileUrls = JSON.parse(fileUrls)
          } catch (e) {
            // 如果解析失败，将字符串包装为数组
            fileUrls = [fileUrls]
          }
        }

        // 确保是数组后再调用 map
        if (Array.isArray(fileUrls)) {
          this.fileList = fileUrls.map(fileUrl => ({
            name: '执行图片', // 从文件路径中提取文件名
            url: fileUrl, // 文件路径
            status: 'success' // 设置文件状态为成功
          }))
          // 更新 smokeCaseDetailForm 中的 file_url 为数组格式
          this.smokeCaseDetailForm.file_url = fileUrls
        } else {
          this.fileList = []
          this.smokeCaseDetailForm.file_url = []
        }
      } else {
        this.fileList = []
        this.smokeCaseDetailForm.file_url = []
      }

      this.handleExecuteSmokeCaseDialogVisible = true
    },
    // 保存用例执行结果(批量执行先注释)
    async submitExecuteCase() {
      let selectedIds = []
      if (this.selectedRows.length === 0 && this.smokeCaseDetailForm) {
        selectedIds = [this.smokeCaseDetailForm.id]
      } else {
        selectedIds = this.selectedRows.map(row => row.id)
      }
      const updateData = {
        id: selectedIds,
        operator: this.user_name,
        file_url: this.smokeCaseDetailForm.file_url,
        operator_result: this.smokeCaseDetailForm.operator_result,
        operator_result_remark: this.smokeCaseDetailForm.operator_result_remark
      }
      const { data: res } = await this.$http.post(this.model + '/update_smoke_case/', updateData)
      if (res.meta.status !== 200) return this.$message.error('处理失败')
      this.$message.success('处理成功')
      this.smokeCaseDetailForm = {}
      this.fileList = []
      this.batchEditSmokeCaseDialogVisible = false
      this.handleExecuteSmokeCaseDialogVisible = false
      await this.queryList()
    },
    // 批量执行冒烟用例
    async batchExcuteSmokeCase() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要执行的用例')
        return
      }

      // 检查选择的用例是否属于同一个迭代
      const firstIterationName = this.selectedRows[0].iteration_name
      const hasInconsistentIteration = this.selectedRows.some(row => row.iteration_name !== firstIterationName)

      if (hasInconsistentIteration) {
        this.$message.warning('请选择同一个迭代用例')
        return
      }

      this.smokeCaseDetailForm.iteration_name = firstIterationName
      this.batchEditSmokeCaseDialogVisible = true
    },
    // 关闭批量冒烟弹窗
    cancelBatchEdit() {
      this.smokeCaseDetailForm = {}
      this.batchEditSmokeCaseDialogVisible = false
    },
    // 关闭执行冒烟用例弹窗
    cancelExecuteEdit() {
      this.smokeCaseDetailForm = {}
      this.fileList = []
      this.handleExecuteSmokeCaseDialogVisible = false
    },
    // ==================== 核心用例tab方法 ====================
    // ==================== AI生成用例tab方法 ====================
    // 编辑AI用例
    async handleUpdateAICase(row) {
      const { data: res } = await this.$http.get(this.model + '/get_ai_case_detail/', { params: { test_case_id: row.id } })
      const objString = JSON.stringify(res.data)
      this.aiCaseDetailForm = JSON.parse(objString)
      this.handleUpdateAICaseDialogVisible = true
    },
    // 保存编辑用例
    async submitUpdateAICase() {
      this.$refs.testCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.post(this.model + '/update_ai_test_case/', this.aiCaseDetailForm)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.aiCaseDetailForm = {}
        this.handleUpdateAICaseDialogVisible = false
        await this.getCaseList()
      })
    },
    // 取消用例编辑
    cancelAICaseEdit() {
      this.aiCaseDetailForm = {}
      this.handleUpdateAICaseDialogVisible = false
    },
    // 更新用例召回状态
    async submitUpdateAICaseStatus(row, status) {
      const { data: res } = await this.$http.post(this.model + '/update_ai_test_case_status/', { id: [row.id], status: status })
      if (res.meta.status !== 200) return this.$message.error('召回状态更新失败')
      this.$message.success('召回状态更新成功')
      await this.getCaseList()
    },
    // ==================== 工具相关方法 ====================
    // 将勾选的数据进行保存
    handleSelectionCase(selection) {
      this.selectedRows = selection
    },
    // 封装用例详情的tooltip
    getCaseDetailContent(row) {
      let content = ''
      // 用例场景
      if (row.case_scene) {
        content += `用例场景：${row.case_scene}\n`
      }
      // 所属系统
      if (row.system) {
        content += `所属系统：${row.system}\n`
      }
      // 模块
      if (row.module) {
        content += `模块：${row.module}\n`
      }
      // 前置条件
      if (row.premise) {
        const premiseLines = row.premise.split('\n').filter(line => line.trim())
        content += '前置条件：\n'
        premiseLines.forEach((line, index) => {
          content += `${line}\n`
        })
      }
      // 测试步骤tooltip文本封装
      if (row.test_steps) {
        const stepsLines = row.test_steps.split('\n').filter(line => line.trim())
        content += '测试步骤：\n'
        stepsLines.forEach((line, index) => {
          content += `${line}\n`
        })
      }
      // 预期结果
      if (row.expected_result) {
        const resultLines = row.expected_result.split('\n').filter(line => line.trim())
        content += '预期结果：\n'
        resultLines.forEach((line, index) => {
          content += `${line}\n`
        })
      }
      return content
    },
    // 将带有编号的字符串按编号分割为数组
    formatSteps(content) {
      // 如果 content 为空或不是字符串，返回空数组
      if (!content || typeof content !== 'string') {
        return []
      }
      // 按数字+分隔符（、:.;）分割
      const steps = content
        .split(/(?=\d+[、：:.])/g) // 在数字+分隔符前分割
        .map(step => step.trim()) // 移除每项两端的空格
        .filter(step => step) // 移除空项
      return steps
    },
    // 判断列表是否有图片
    hasImage(contentList) {
      // 判断 contentList 是否有值
      return contentList?.length > 0
    },
    // 预览图片
    previewImages(contentList) {
      this.previewImagesList = contentList
      this.dialogVisible = true
      this.currentImageIndex = 0 // 每次打开重置为第一张
    },
    // 查看图片-上一张
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 查看图片-下一张
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    // 键盘事件监听器,实现左右键切换图片
    handleKeydown(event) {
      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },
    // 上传成功回调
    handleUploadSuccess(response, file, fileList) {
      if (response.meta.status === 200) {
        // 确保使用Vue的响应式数组更新
        this.$set(this.smokeCaseDetailForm, 'file_url', [
          ...(this.smokeCaseDetailForm.file_url ? this.smokeCaseDetailForm.file_url : []),
          response.data.fileUrl
        ])
        // 更新fileList中的URL为实际的服务器URL
        const index = this.fileList.findIndex(f => f.uid === file.uid)
        if (index !== -1) {
          this.$set(this.fileList, index, {
            ...this.fileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100 // 上传成功设置为100%
          })
        }
      }
    },
    handFileList(file, fileList) {
      this.fileList = fileList
    },
    // 删除文件回调
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      if (index > -1) {
        this.smokeCaseDetailForm.file_url.splice(index, 1)
        this.fileList.splice(index, 1)
      }
      // 更新文件列表
      this.fileList = this.fileList.filter(f => f.uid !== file.uid)
    },
    // 文件超出限制回调
    handleExceed(files, fileList) {
      this.$message.warning('最多只允许上传3张图片')
    },
    // 预览图片
    handlePreview() {
      const previewList = this.smokeCaseDetailForm.file_url.map(fileUrl => {
        return fileUrl
      })
      this.previewImages(previewList)
    },
    // 上传前校验：限制为 PNG 和 JPG 格式，并检查大小
    beforeUpload(file) {
      // 只允许的 MIME 类型
      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      // 只允许的扩展名
      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }

      return isValidType && isValidExtension && isLt5M
    },
    // 处理粘贴事件
    async handlePastePic(event) {
      // 获取剪贴板数据，兼容不同浏览器的事件对象
      const items = (event.clipboardData || event.originalEvent.clipboardData).items
      // 用于存储检测到的图片文件
      const imageFiles = []
      // 遍历剪贴板中的所有项
      for (const item of items) {
        // 检查是否为图片类型（MIME类型以'image'开头）
        if (item.type.indexOf('image') !== -1) {
          // 将剪贴板项转换为文件对象
          const file = item.getAsFile()
          // 在上传前校验文件（大小、格式等），beforeUpload 返回布尔值
          const isValid = await this.beforeUpload(file)
          if (isValid) {
            // 如果文件有效，添加到图片文件数组
            imageFiles.push(file)
          }
        }
      }
      // 如果检测到有效的图片文件
      if (imageFiles.length > 0) {
        event.preventDefault() // 阻止默认粘贴行为
        // 调用上传方法处理这些图片文件
        this.uploadPastedImages(imageFiles)
      }
    },
    // 处理粘贴的图片并自动上传
    uploadPastedImages(files) {
      // 遍历所有图片文件
      files.forEach(async (file, index) => {
        // 检查当前文件列表是否已达到上限（3个）
        if (this.fileList.length >= 3) {
          // 如果超过限制，触发超出处理函数
          this.handleExceed()
          return
        }

        // 创建临时文件对象，用于在 el-upload 组件中显示
        const tempFile = {
          name: file.name || `pasted-image-${index}.png`, // 提供默认文件名
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0 // 初始化进度为0
        }

        // 使用 Vue.set 添加文件到 fileList，确保响应式更新
        this.$set(this.fileList, this.fileList.length, tempFile)

        // 自动上传逻辑
        try {
          // 创建 FormData 对象用于文件上传
          const formData = new FormData()
          // 将文件添加到 FormData，键名为 'file'
          formData.append('file', file)
          // 调用上传文件方法
          const response = await this.$http.post(this.uploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              // 更新上传进度
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.fileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            // 调用成功处理函数，更新文件状态和URL
            this.handleUploadSuccess(response.data, tempFile, this.fileList)
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.$set(this.fileList[fileIndex], 'status', 'failed')
            this.$set(this.fileList[fileIndex], 'percentage', 0)
          }
          this.$message.error('图片上传失败')
        }
      })
    }
  }
}
</script>

<style scoped>
.table-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
}

.table-container .el-table {
  min-width: 100%;
  table-layout: fixed;
}

.table-container .el-table th,
.table-container .el-table td {
  white-space: nowrap;
}

/* 确保表格列宽保持一致 */
.table-container .el-table .el-table__header-wrapper,
.table-container .el-table .el-table__body-wrapper {
  overflow-x: visible;
}

/* 优化滚动条样式 */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自定义紧凑按钮样式 - 减少内边距 */
.compact-button {
  padding: 7px 10px !important;
  font-size: 12px;
  min-width: auto !important;
}

/* 针对不同类型按钮的紧凑样式 */
.compact-button.el-button--primary {
  padding: 7px 2px !important;
}

.compact-button.el-button--success {
  padding: 7px 2px !important;
}

.compact-button.el-button--danger {
  padding: 7px 2px !important;
}

/* 操作按钮容器样式 - 控制按钮间距和整体布局 */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px; /* 按钮之间的间距 */
  padding: 0 2px; /* 容器左右内边距 */
  width: 100%; /* 确保容器占满整个单元格宽度 */
  height: 100%; /* 确保容器占满整个单元格高度 */
  min-height: 32px; /* 设置最小高度确保垂直居中 */
}

/* 优化表格操作列的内边距 */
.el-table .el-table__cell {
  padding: 4px 2px !important;
}

/* 专门针对操作列的样式优化 - 确保居中 */
.el-table-column--selection .cell,
.el-table__fixed-right .cell {
  padding: 4px 2px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}
</style>
